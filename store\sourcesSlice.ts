import { createSlice, type PayloadAction } from "@reduxjs/toolkit"
import type { SourceConfig } from "@/types"

export interface SourcesState {
  sources: {
    rossDatabase: { enabled: boolean; config: SourceConfig }
    linkedin: { enabled: boolean; config: SourceConfig }
    email: { enabled: boolean; config: SourceConfig }
    feedback: { enabled: boolean; config: SourceConfig }
  }
}

const initialState: SourcesState = {
  sources: {
    rossDatabase: {
      enabled: false,
      config: {
        id: "rossDatabase",
        name: "Ross Database",
        type: "ross",
        colorClass: "text-gray-600",
        enabled: true,
      },
    },
    linkedin: {
      enabled: true,
      config: {
        id: "rossFiles",
        name: "Ross Files, Docs, PDFs",
        type: "ross",
        colorClass: "text-blue-600",
        enabled: true,
      },
    },
    email: {
      enabled: true,
      config: {
        id: "sutraWebSearch",
        name: "Sutra Web Search",
        type: "sutra",
        colorClass: "text-red-500",
        enabled: true,
      },
    },
    feedback: {
      enabled: true,
      config: {
        id: "sutraFeedback",
        name: "Sutra Feedback Analysis",
        type: "sutra",
        colorClass: "text-purple-600",
        enabled: true,
      },
    },
  },
}

const sourcesSlice = createSlice({
  name: "sources",
  initialState,
  reducers: {
    toggleSource(state, action: PayloadAction<keyof SourcesState["sources"]>) {
      state.sources[action.payload].enabled = !state.sources[action.payload].enabled
    },
    setSourceEnabled(state, action: PayloadAction<{ sourceId: keyof SourcesState["sources"]; enabled: boolean }>) {
      state.sources[action.payload.sourceId].enabled = action.payload.enabled
    },
  },
})

export const { toggleSource, setSourceEnabled } = sourcesSlice.actions
export default sourcesSlice.reducer

import type React from "react"
import { useState } from "react"
import type { ThinkingStep, ThinkingStepStatus } from "@/types"
import { CheckCircle2, Loader2, Circle, ChevronRight, XCircle, ChevronDown, ExternalLink, FileText, Tag } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { MarkdownMessage } from "./markdown-message"

interface ThinkingStepProps {
  step: ThinkingStep
  isLastStep?: boolean
}

// Type definitions for different agent results
interface UserIntentResult {
  tags: string[]
  filters: Record<string, any>
}

interface WebSearchResult {
  text: string
  sources: Array<{
    title: string
    url: string
  }>
}

interface ProductUnstructuredResult {
  content: string
  sources: Array<{
    pdf: string
    page_start: number
    page_end: number
  }>
}

interface FeedbackResult {
  Question: string
  Feedback: string
  Similarity_Score: number
}

const statusIcons: Record<ThinkingStepStatus, React.ElementType> = {
  completed: CheckCircle2,
  "in-progress": Loader2,
  pending: Circle,
  error: XCircle,
}

const statusColors: Record<ThinkingStepStatus, string> = {
  completed: "text-teal-500",
  "in-progress": "text-blue-500 animate-spin",
  pending: "text-gray-400",
  error: "text-red-500",
}

export function ThinkingStepComponent({ step }: ThinkingStepProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const IconComponent = statusIcons[step.status]

  // Parse the result from the step details if available
  const hasResult = step.status === "completed" && step.details
  const shouldShowExpander = hasResult && step.id !== 'supervisor_agent'

  // Parse agent result from details
  let agentResult = null
  if (hasResult && step.details) {
    try {
      // Try to parse the details as JSON (should be the agent result)
      agentResult = JSON.parse(step.details)
    } catch (error) {
      // If parsing fails, the details might be a plain message
      // Try to extract JSON from the details if it contains structured data
      try {
        const jsonMatch = step.details.match(/\{[\s\S]*\}/)
        if (jsonMatch) {
          agentResult = JSON.parse(jsonMatch[0])
        }
      } catch (innerError) {
        // If all parsing fails, we'll show raw details
        console.log('Failed to parse agent result:', error)
      }
    }
  }

  const toggleExpanded = () => {
    if (shouldShowExpander) {
      setIsExpanded(!isExpanded)
    }
  }

  return (
    <div className="rounded-lg">
      <div
        className={`flex items-center justify-between py-1 px-4 ${shouldShowExpander ? 'cursor-pointer hover:bg-gray-100' : ''} transition-colors`}
        onClick={toggleExpanded}
      >
        <div className="flex items-center gap-4 flex-1">
          <IconComponent className={`h-5 w-5 ${statusColors[step.status]} flex-shrink-0`} />
          <div className="flex-1">
            <p
              className={`text-base font-medium ${
                step.status === "completed"
                  ? "text-gray-900"
                  : step.status === "in-progress"
                    ? "text-blue-700"
                    : step.status === "error"
                      ? "text-red-700"
                      : "text-gray-600"
              }`}
            >
              { step.description === "supervisor_agent" ? (step.status === "in-progress" ? "Compiling with Sutra Intelligence..." : "Compiled with Sutra Intelligence") : (step.status === "in-progress" ? step.description + "..." : step.description)}
            </p>
            {step.status === "error" && step.error && (
              <p className="text-sm text-red-600 mt-1">
                {step.error}
              </p>
            )}
          </div>
        </div>

        {shouldShowExpander && (
          <ChevronRight
            className={`h-5 w-5 text-gray-900 flex-shrink-0 transition-transform ${
              isExpanded ? 'rotate-90' : ''
            }`}
          />
        )}
      </div>

      {/* Expandable content */}
      {isExpanded && shouldShowExpander && (
        <div className="px-4 pb-4 border-t border-gray-100">
          <AgentResultDisplay agentId={step.id} result={agentResult} rawDetails={step.details} />
        </div>
      )}
    </div>
  )
}

// Component to display different agent results
function AgentResultDisplay({ agentId, result, rawDetails }: {
  agentId: string
  result: any
  rawDetails?: string
}) {
  if (!result && !rawDetails) {
    return <p className="text-sm text-gray-500 mt-2">No result data available</p>
  }

  // Handle different agent types
  switch (agentId) {
    case 'user_intent_agent':
      return <UserIntentDisplay result={result} />
    case 'web_search_agent':
      return <WebSearchDisplay result={result} />
    case 'product_unstructured_agent':
      return <ProductUnstructuredDisplay result={result} />
    case 'database_search_agent':
      return <DatabaseSearchDisplay result={result} />
    case 'feedback_agent':
      return <FeedbackDisplay result={result} />
    default:
      return <DefaultResultDisplay rawDetails={rawDetails} />
  }
}

// User Intent Agent Display
function UserIntentDisplay({ result }: { result: UserIntentResult }) {
  if (!result) return null

  return (
    <div className="mt-3 space-y-3">
      {result.tags && result.tags.length > 0 && (
        <div>
          <h4 className="text-sm font-medium text-gray-700 mb-2 flex items-center gap-1">
            <Tag className="h-4 w-4" />
            Tags
          </h4>
          <div className="flex flex-wrap gap-2">
            {result.tags.map((tag, index) => (
              <Badge key={index} variant="secondary" className="text-xs">
                {tag}
              </Badge>
            ))}
          </div>
        </div>
      )}

      {result.filters && Object.keys(result.filters).length > 0 && (
        <div>
          <h4 className="text-sm font-medium text-gray-700 mb-2">Filters</h4>
          <div className="flex flex-wrap gap-2">
            {Object.entries(result.filters).map(([key, value], index) => (
              <Badge key={index} variant="outline" className="text-xs">
                {key}: {String(value)}
              </Badge>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

// Web Search Agent Display
function WebSearchDisplay({ result }: { result: WebSearchResult }) {
  if (!result) return null

  return (
    <div className="mt-3 space-y-3">
      {result.text && (
        <div>
          <h4 className="text-sm font-medium text-gray-700 mb-2">Search Results</h4>
          <div className="prose prose-sm max-w-none">
            <MarkdownMessage content={result.text} />
          </div>
        </div>
      )}

      {result.sources && result.sources.length > 0 && (
        <div>
          <h4 className="text-sm font-medium text-gray-700 mb-2">Sources</h4>
          <div className="space-y-2">
            {result.sources.map((source, index) => (
              <a
                key={index}
                href={source.url}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-2 p-2 rounded-md border border-gray-200 hover:bg-gray-50 transition-colors"
              >
                <ExternalLink className="h-4 w-4 text-blue-500 flex-shrink-0" />
                <span className="text-sm text-blue-600 hover:underline truncate">
                  {source.title}
                </span>
              </a>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

// Product Unstructured Agent Display
function ProductUnstructuredDisplay({ result }: { result: ProductUnstructuredResult }) {
  if (!result) return null

  return (
    <div className="mt-3 space-y-3">
      {result.content && (
        <div>
          <h4 className="text-sm font-medium text-gray-700 mb-2">Content</h4>
          <div className="prose prose-sm max-w-none">
            <MarkdownMessage content={result.content} />
          </div>
        </div>
      )}

      {result.sources && result.sources.length > 0 && (
        <div>
          <h4 className="text-sm font-medium text-gray-700 mb-2">Document Sources</h4>
          <div className="flex flex-wrap gap-2">
            {result.sources.map((source, index) => (
              <Badge key={index} variant="outline" className="text-xs flex items-center gap-1">
                <FileText className="h-3 w-3" />
                {source.pdf} ({source.page_start}-{source.page_end})
              </Badge>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

// Database Search Agent Display (similar to product unstructured)
function DatabaseSearchDisplay({ result }: { result: any }) {
  if (!result) return null

  // Handle similar structure to product unstructured
  if (result.content || result.text) {
    return (
      <div className="mt-3 space-y-3">
        <div>
          <h4 className="text-sm font-medium text-gray-700 mb-2">Database Results</h4>
          <div className="prose prose-sm max-w-none">
            <MarkdownMessage content={result.content || result.text || ''} />
          </div>
        </div>

        {result.sources && result.sources.length > 0 && (
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-2">Sources</h4>
            <div className="flex flex-wrap gap-2">
              {result.sources.map((source: any, index: number) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {source.name || source.title || `Source ${index + 1}`}
                </Badge>
              ))}
            </div>
          </div>
        )}
      </div>
    )
  }

  return <DefaultResultDisplay rawDetails={JSON.stringify(result, null, 2)} />
}

// Feedback Agent Display
function FeedbackDisplay({ result }: { result: FeedbackResult[] }) {
  if (!result || !Array.isArray(result)) return null

  return (
    <div className="mt-3 space-y-3">
      <h4 className="text-sm font-medium text-gray-700 mb-2">Feedback Results</h4>
      <div className="space-y-3">
        {result.map((feedback, index) => (
          <div key={index} className="p-3 rounded-md border border-gray-200 bg-gray-50">
            <div className="space-y-2">
              <div>
                <span className="text-xs font-medium text-gray-600">Question:</span>
                <p className="text-sm text-gray-800 mt-1">{feedback.Question}</p>
              </div>
              <div>
                <span className="text-xs font-medium text-gray-600">Feedback:</span>
                <p className="text-sm text-gray-800 mt-1">{feedback.Feedback}</p>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-xs font-medium text-gray-600">Similarity Score:</span>
                <Badge variant="secondary" className="text-xs">
                  {(feedback.Similarity_Score * 100).toFixed(1)}%
                </Badge>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

// Default display for unknown result types
function DefaultResultDisplay({ rawDetails }: { rawDetails?: string }) {
  if (!rawDetails) return null

  return (
    <div className="mt-3">
      <h4 className="text-sm font-medium text-gray-700 mb-2">Raw Result</h4>
      <pre className="text-xs bg-gray-100 p-3 rounded-md overflow-auto max-h-40">
        {rawDetails}
      </pre>
    </div>
  )
}

"use client"

import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Send } from "lucide-react"

interface ChatInputSectionProps {
  inputValue: string
  onInputChange: (value: string) => void
  onSend: () => void
  onSuggestedClick: (query: string) => void
  placeholder: string
  suggestedQueries: string[]
  relatedTriggers?: boolean
  disabled?: boolean
}

export function ChatInputSection({
  inputValue,
  onInputChange,
  onSend,
  onSuggestedClick,
  placeholder,
  suggestedQueries,
  relatedTriggers = false,
  disabled = false,
}: ChatInputSectionProps) {
  return (
    <div className={`space-y-3 ${relatedTriggers ? "" : "mb-[12rem]"}`}>
      <div className="relative">
        <Textarea
          placeholder={placeholder}
          className="w-full p-3 pr-20 rounded-2xl border-gray-300/80 shadow-sm focus:border-teal-500 focus:ring-teal-500 focus-visible:outline-0 text-base min-h-[70px] resize-none disabled:opacity-50"
          value={inputValue}
          onChange={(e) => onInputChange(e.target.value)}
          disabled={disabled}
          name="chat-input"
          onKeyDown={(e) => {
            if (e.key === "Enter" && !e.shiftKey && !disabled) {
              e.preventDefault()
              onSend()
            }
          }}
        />
        <Button
          size="icon"
          className="absolute top-3 right-6 rounded-full bg-teal-400 hover:bg-teal-500 h-12 w-12 disabled:opacity-50"
          onClick={onSend}
          disabled={!inputValue.trim() || disabled}
        >
          <Send className="h-5 w-5 text-white" />
          <span className="sr-only">Send</span>
        </Button>
      </div>

      {/* Suggested queries with better spacing */}
      {!disabled && (
        <div className={`flex items-center ${relatedTriggers ? "" : "justify-center"} `}>
          {relatedTriggers && <p className="text-base font-medium text-gray-700 mr-4">Related Triggers:</p>}
          <div className="flex flex-wrap gap-4">
            {suggestedQueries.map((query) => (
              <Button
                key={query}
                variant="outline"
                className="justify-between w-fit h-auto py-2 px-3 rounded-xl border-gray-300/80 bg-white hover:bg-gray-50 text-left shadow-sm"
                onClick={() => onSuggestedClick(query)}
              >
                <span className="text-base font-normal text-gray-700 whitespace-normal leading-relaxed">{query}</span>
                <Send className="h-4 w-4 text-gray-400 flex-shrink-0 ml-4" />
              </Button>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
